/**
 * 翻译页面节点
 */
import { MessageType } from '@src/common/const'
import { loadingIcon } from '@src/common/images'
import { blankNodeList, ELE_MIN_WIDTH, translateClassname } from '../const'
import { callHiddenChatUIOnsend } from './hiddenChatUIHelper'
import { buildApiUrl } from '@src/common/utils/baseUrl'
import { getUserId } from '@src/common/utils/userConfigApi'
import { PROJECTID } from '@src/config/aiChatConfig'

const { translateIcon, translationResult, splitParagraph, segmentWrapper, floatButtonTooltipTitle } = translateClassname

// 全局翻译状态管理器
interface TranslationState {
  isPageTranslated: boolean
  isTranslating: boolean
  activeRequests: Map<string, AbortController>
}

// 声明全局类型
declare global {
  interface Window {
    __translationState?: TranslationState
  }
}

// 初始化全局翻译状态
function initGlobalTranslationState(): TranslationState {
  if (!window.__translationState) {
    window.__translationState = {
      isPageTranslated: false,
      isTranslating: false,
      activeRequests: new Map()
    }
  }
  return window.__translationState
}

// 获取全局翻译状态
function getGlobalTranslationState(): TranslationState {
  return initGlobalTranslationState()
}

// 设置页面翻译状态
function setPageTranslated(translated: boolean): void {
  const state = getGlobalTranslationState()
  state.isPageTranslated = translated

  // 触发状态变更事件，通知React组件更新
  window.dispatchEvent(new CustomEvent('translationStateChanged', {
    detail: { isPageTranslated: translated }
  }))
}

// 设置翻译进行状态
function setTranslating(translating: boolean): void {
  const state = getGlobalTranslationState()
  state.isTranslating = translating
}

// 添加活跃请求
function addActiveRequest(requestId: string, controller: AbortController): void {
  const state = getGlobalTranslationState()
  state.activeRequests.set(requestId, controller)
}

// 移除活跃请求
function removeActiveRequest(requestId: string): void {
  const state = getGlobalTranslationState()
  state.activeRequests.delete(requestId)
}

// 取消所有活跃请求
function cancelAllActiveRequests(): void {
  const state = getGlobalTranslationState()
  state.activeRequests.forEach((controller, requestId) => {
    console.log(`取消翻译请求: ${requestId}`)
    controller.abort()
  })
  state.activeRequests.clear()
}

// 全局变量管理
let textCollectionQueue: Array<{
  icon: HTMLElement
  textContent: string
  id: string
  paragraph: HTMLElement
  segmentContainer?: HTMLElement // 新增：段落分段容器
  htmlStructure?: Array<{
    type: 'text' | 'element'
    content: string
    tagName?: string
    attributes?: Record<string, string>
  }>
}> = []
let batchProcessTimer: NodeJS.Timeout | null = null
const BATCH_DELAY = 500 // 500ms延迟批量处理
const MIN_CHARS_FOR_BATCH = 500 // 少于2000字符时进行批量处理
let currentGroups = []

// 全局观察者管理
let globalIntersectionObserver: IntersectionObserver | null = null
let globalMutationObserver: MutationObserver | null = null

// 创建 Intersection Observer 实例
function createIntersectionObserver(): IntersectionObserver {
  return new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        // 在处理前检查翻译状态
        const state = getGlobalTranslationState()
        if (!state.isPageTranslated) {
          console.log('翻译已取消，跳过观察者回调')
          return
        }
        // 只处理可视区的元素
        if (entry.isIntersecting) {
          const targetElement = entry.target as HTMLElement
          // 跳过悬浮球 Tooltip 内的元素
          if (targetElement.closest(`.${floatButtonTooltipTitle}`)) {
            globalIntersectionObserver?.unobserve(targetElement)
            return
          }
          // console.log('开始观察targetElement', entry.target);

          processTextNodesInElement(targetElement)
          // 一旦处理过，就不再观察这个元素
          globalIntersectionObserver?.unobserve(entry.target)
        }
      })
    },
    {
      root: null, // 相对于视口
      rootMargin: '100px', // 提前100px开始检测
      threshold: 0, // 元素有20%进入可视区就触发
    }
  )
}

// 创建 Mutation Observer 实例
function createMutationObserver(): MutationObserver {
  return new MutationObserver((mutations) => {
    // 在处理前检查翻译状态
    const state = getGlobalTranslationState()
    if (!state.isPageTranslated) {
      console.log('翻译已取消，跳过变化观察者回调')
      return
    }

    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement

          // 避免处理翻译相关的元素
          if (
            element.classList.contains(translateIcon) ||
            element.classList.contains(translationResult) ||
            element.classList.contains(splitParagraph) ||
            element.classList.contains(segmentWrapper)
          ) {
            return
          }

          // 避免处理悬浮球 Tooltip 内的元素
          if (
            element.classList.contains(floatButtonTooltipTitle) ||
            element.closest(`.${floatButtonTooltipTitle}`)
          ) {
            return
          }

          // 避免处理翻译元素的子元素
          if (
            element.closest(`.${translateIcon}`) ||
            element.closest(`.${translationResult}`) ||
            element.closest(`.${splitParagraph}`) ||
            element.closest(`.${segmentWrapper}`)
          ) {
            return
          }

          // 对新添加的元素进行观察
          globalIntersectionObserver?.observe(element)
          // 处理新添加元素中的所有子元素
          observerChildElement(element)
        }
      })
    })
  })
}

// 启动全局观察者
function startGlobalObserver(): void {
  console.log('启动全局观察者...')

  // 如果已有观察者，先停止
  stopGlobalObserver()

  // 创建新的观察者实例
  globalIntersectionObserver = createIntersectionObserver()
  globalMutationObserver = createMutationObserver()

  // 启动 MutationObserver 监听DOM变化
  globalMutationObserver.observe(document.body, {
    childList: true,
    subtree: true,
  })

  console.log('全局观察者已启动')
}

// 停止全局观察者
function stopGlobalObserver(): void {
  console.log('停止全局观察者...')

  // 停止 IntersectionObserver
  if (globalIntersectionObserver) {
    globalIntersectionObserver.disconnect()
    globalIntersectionObserver = null
    console.log('IntersectionObserver 已停止')
  }

  // 停止 MutationObserver
  if (globalMutationObserver) {
    globalMutationObserver.disconnect()
    globalMutationObserver = null
    console.log('MutationObserver 已停止')
  }

  console.log('全局观察者已停止')
}

// 检查元素是否隐藏
function isHidden(node: Node): boolean {
  if (!(node instanceof Element)) return false

  const style = window.getComputedStyle(node)
  return (
    style.display === 'none' ||
    style.visibility === 'hidden' ||
    style.opacity === '0' ||
    node.hasAttribute('hidden')
  )
}

// 处理单个元素中的文本节点
function processTextNodesInElement(element: HTMLElement) {
  if (blankNodeList.includes(element.tagName)) {
    return
  }
  if (element.classList.contains(translateIcon)) {
    return
  }
  // 避免处理翻译结果元素
  if (element.classList.contains(translationResult)) {
    return
  }
  // 避免处理悬浮球 Tooltip 内的元素
  if (
    element.classList.contains(floatButtonTooltipTitle) ||
    element.closest(`.${floatButtonTooltipTitle}`)
  ) {
    return
  }
  // 避免处理已经有翻译结果的段落中的新增元素
  if (element.closest(`.${translationResult}`)) {
    return
  }
  // 避免处理已拆分的段落
  if (element.classList.contains(splitParagraph)) {
    return
  }
  // 避免处理分段包装容器
  if (element.classList.contains(segmentWrapper)) {
    return
  }
  // 避免处理已拆分段落中的元素
  if (element.closest(`.${splitParagraph}`)) {
    return
  }
  // 隐藏的元素不翻译
  if (isHidden(element)) {
    return
  }

  // 存储段落及其文本节点的映射
  const paragraphMap = new Map<HTMLElement | Text, Text[]>()

  const walker = document.createTreeWalker(element, NodeFilter.SHOW_TEXT, {
    acceptNode: function (node) {
      // 过滤掉空白文本
      if (!node.textContent?.trim()) {
        return NodeFilter.FILTER_REJECT
      }
      // 过滤悬浮球 Tooltip 内的文本
      const parent = (node as Text).parentElement
      if (parent && (parent.classList.contains(floatButtonTooltipTitle) || parent.closest(`.${floatButtonTooltipTitle}`))) {
        return NodeFilter.FILTER_REJECT
      }
      return NodeFilter.FILTER_ACCEPT
    },
  })

  let node: Node | null
  while ((node = walker.nextNode())) {
    const textNode = node as Text
    const parentElement = textNode.parentElement

    if (!parentElement) {
      continue
    }

    // 检查是否在黑名单元素内
    const isBlankEle = blankNodeList.find((item) =>
      parentElement.closest(item.toLowerCase())
    )
    if (isBlankEle) {
      continue
    }

    // 避免处理翻译结果元素内的文本
    if (parentElement.closest(`.${translationResult}`)) {
      continue
    }

    // 避免处理翻译图标元素内的文本
    if (parentElement.closest(`.${translateIcon}`)) {
      continue
    }

    // 检查元素宽度是否小于最小宽度
    const clientWidth = parentElement.getBoundingClientRect().width // 获取父元素的宽度
    if (clientWidth <= ELE_MIN_WIDTH) {
      continue
    }

    // console.log('processTextNodesInElement: parentElement', textNode, parentElement, element, parentElement === element);


    // 使用父节点作为段落容器，如果父节点是传入的element则使用TextNode作为key
    let paragraph: HTMLElement | Text
    //如果该节点是父元素的直接文字节点，且该节点的兄弟节点是一个块元素
    console.log('processTextNodesInElement: parentElement', parentElement, element, textNode.nextSibling, textNode.nextSibling && isBlockElementByTag(textNode.nextSibling));

    if (parentElement === element && textNode.nextSibling && isBlockElementByTag(textNode.nextSibling)) {
      // console.log('文本节点，这里应该要存到paragraphMap中', textNode);

      // 如果是直接在element下的文本节点，使用TextNode作为key
      paragraph = textNode
    } else {
      // 否则使用父节点作为key
      paragraph = findNearestBlockElement(parentElement)
    }

    // 将文本节点添加到对应的段落映射中
    if (!paragraphMap.has(paragraph)) {
      paragraphMap.set(paragraph, [])
    }
    paragraphMap.get(paragraph)!.push(textNode)
  }


  // 为每个段落添加翻译图标
  paragraphMap.forEach((textNodes, paragraph) => {
    if (paragraph instanceof Text) {
      addTranslateIconForTextNode(paragraph, textNodes)
      return
    }
    if (
      !paragraph.querySelector(`.${translateIcon}`) &&
      !paragraph.querySelector(`.${translationResult}`) &&
      !paragraph.classList.contains(splitParagraph)
    ) {
      addTranslateIcon(paragraph, textNodes)
    }
  })
}

function isBlockElementByTag(element) {
  // 常见的默认块级元素标签集合
  const blockTags = new Set([
    'DIV', 'P', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6',
    'UL', 'OL', 'LI', 'DL', 'DT', 'DD', 'TABLE',
    'FORM', 'HEADER', 'FOOTER', 'SECTION', 'ARTICLE'
  ]);
  // 判断元素标签名是否在块级标签集合中
  return blockTags.has(element.tagName);
}

// 为纯文本节点添加翻译图标
function addTranslateIconForTextNode(textNode: Text, _textNodes: Text[]) {
  // 检查文本节点是否有有效内容
  const textContent = textNode.textContent?.trim()
  if (!textContent) {
    return
  }

  // 获取文本节点的父元素
  const parentElement = textNode.parentElement
  if (!parentElement) {
    return
  }


  // 检查这个特定文本节点是否已经有翻译图标
  // 通过检查紧邻的下一个兄弟节点是否为翻译图标
  if (textNode.nextSibling &&
    textNode.nextSibling.nodeType === Node.ELEMENT_NODE &&
    (textNode.nextSibling as Element).classList.contains(translateIcon)) {
    return
  }

  // 创建翻译图标
  const icon = document.createElement('span')
  icon.className = translateIcon
  icon.style.cssText = `
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-left: 4px;
    background-image: url(${loadingIcon});
    background-size: contain;
    background-repeat: no-repeat;
    vertical-align: middle;
  `

  // 直接在文本节点后面插入翻译图标
  if (textNode.nextSibling) {
    parentElement.insertBefore(icon, textNode.nextSibling)
  } else {
    parentElement.appendChild(icon)
  }

  // 提取文本节点的结构化内容
  const htmlStructure = [{
    type: 'text' as const,
    content: textContent
  }]

  // 处理翻译，使用文本节点的父元素作为段落容器
  handleTranslateTextContent(
    icon,
    textContent,
    parentElement,
    htmlStructure
  )
}

// 查找最近的块级元素
function findNearestBlockElement(element: HTMLElement): HTMLElement | null {
  const blockElements = [
    'P',
    'DIV',
    'SECTION',
    'ARTICLE',
    'HEADER',
    'FOOTER',
    'H1',
    'H2',
    'H3',
    'H4',
    'H5',
    'H6',
    'UL',
    'OL',
    'LI',
    'BLOCKQUOTE',
    'SPAN',
    'A',
    'DT',
    'DD',
    // 'STRONG'
  ]

  let current = element
  while (current && current !== document.body) {
    if (blockElements.includes(current.tagName)) {
      return current
    }
    current = current.parentElement
  }
  return element
}

// 检查段落是否包含br标签，如果包含则拆分处理
function shouldSplitParagraph(paragraph: HTMLElement): boolean {
  return paragraph.querySelector('br') !== null
}

// 分析段落中的br标签位置，返回分段信息
function analyzeParagraphSegments(paragraph: HTMLElement): Array<{
  htmlContent: string
  textContent: string
  htmlStructure: Array<{
    type: 'text' | 'element'
    content: string
    tagName?: string
    attributes?: Record<string, string>
  }>
}> {
  const segments: Array<{
    htmlContent: string
    textContent: string
    htmlStructure: Array<{
      type: 'text' | 'element'
      content: string
      tagName?: string
      attributes?: Record<string, string>
    }>
  }> = []

  // 创建一个临时容器来处理段落内容
  const tempContainer = document.createElement('div')
  tempContainer.innerHTML = paragraph.innerHTML

  // 移除已存在的翻译相关元素
  tempContainer.querySelectorAll(`.${translateIcon}, .${translationResult}`).forEach(el => el.remove())

  // 按br标签分割内容
  const parts = tempContainer.innerHTML.split(/<br\s*\/?>/i)

  parts.forEach((part, index) => {
    const trimmedPart = part.trim()
    if (trimmedPart) {
      // 创建临时元素来提取结构化内容
      const tempElement = document.createElement('div')
      tempElement.innerHTML = trimmedPart

      const structuredContent = extractStructuredContent(tempElement)

      segments.push({
        htmlContent: trimmedPart,
        textContent: structuredContent.textContent,
        htmlStructure: structuredContent.htmlStructure
      })
    }
  })

  return segments
}

// 生成唯一ID
function generateUniqueId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

// 批量处理翻译
function processBatchTranslation() {
  if (textCollectionQueue.length === 0) return

  // 动态分组处理，确保每组字符数不超过MIN_CHARS_FOR_BATCH
  const groups = groupTextsByCharLimit(textCollectionQueue, MIN_CHARS_FOR_BATCH)
  currentGroups = groups

  console.log('发起请求 currentGroups', currentGroups);

  groups.forEach((group) => {
    if (group.length > 1) {
      // 批量处理
      const batchTexts = group.map((item) => item.textContent)
      const batchQuery = batchTexts.join('###')
      const batchIds = group.map((item) => item.id)

      // callHiddenChatUIOnsend('text', `${batchQuery}`, {
      //   agentId: 'translate',
      //   extendParams: {
      //     msgChannel: 'translatePage',
      //   }
      // })
      handleTranslate(
        batchQuery,
        '',
        (response) => {


          if (response?.code === '0') {
            // 使用智能分割函数处理批量翻译结果
            // const results = splitBatchTranslationResult(response.result, group.length)
            const results = response.result.split('###')
            console.log('group.length >>> 1 response', response, group, results);
            group.forEach((item, index) => {
              if (results[index]) {
                updateIconWithTranslation(
                  item.icon,
                  results[index],
                  item.paragraph,
                  item.htmlStructure,
                  item.segmentContainer
                )
              }
            })
          }
        }
      )
    } else {
      // 单独处理
      const item = group[0]
      // callHiddenChatUIOnsend('text', `${item.textContent}`, {
      //   agentId: 'translate',
      //   extendParams: {
      //     msgChannel: 'translatePage',
      //   }
      // })
      handleTranslate(
        item.textContent,
        '',
        (response) => {
          console.log('group.length === 1 response', response);

          if (response?.code === '0') {
            updateIconWithTranslation(
              item.icon,
              response.result,
              item.paragraph,
              item.htmlStructure,
              item.segmentContainer
            )
          }
        }
      )
    }
  })

  // 清空队列
  textCollectionQueue = []
}

// 处理翻译请求
const handleTranslate = async (query, conversationID, sendResponse) => {
  const userId = await getUserId()
  const requestId = generateUniqueId()

  // 创建AbortController用于取消请求
  const abortController = new AbortController()
  addActiveRequest(requestId, abortController)

  try {
    // 在发起请求前检查翻译状态
    const state = getGlobalTranslationState()
    if (!state.isPageTranslated) {
      console.log('翻译已被取消，跳过请求:', requestId)
      removeActiveRequest(requestId)
      return
    }

    const chatResponse = await fetch(
      `${buildApiUrl('/chat/workflow/chrome-v2')}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appId: "web-assistant",
          userId: userId,
          agentId: "translate",
          question: query,
          hideConversation: true,
          conversationId: '',
          projectId: PROJECTID
        }),
        signal: abortController.signal // 添加取消信号
      }
    )
    const chatRes = await chatResponse.text()
    console.log('翻译响应:', {
      query,
      chatRes
    })
    // 处理SSE流式数据，累积拼接所有翻译内容
    const lines = chatRes.split('\n\n').filter(line => line.trim().startsWith('data:'));
    let accumulatedTranslation = ''; // 累积的翻译结果
    let isStreamEnded = false; // 流是否结束

    for (const line of lines) {
      try {
        // 移除 'data:' 前缀并清理尾部换行符
        const jsonStr = line.replace(/^data:/, '').trim();
        // console.log('jsonStr', jsonStr);

        // 跳过空行或无效数据
        if (!jsonStr) continue;

        const jsonObj = JSON.parse(jsonStr);
        // console.log('jsonObj', jsonObj)

        // 检查是否是流结束标记
        if (jsonObj && jsonObj.msg === "stream end!") {
          isStreamEnded = true;
          console.log('检测到流结束标记');
          break; // 结束循环
        }

        // 检查是否是包含翻译结果的数据
        if (jsonObj && jsonObj.resultData && jsonObj.resultData.answerInfos) {
          const answerInfos = jsonObj.resultData.answerInfos;

          // 遍历 answerInfos 数组查找翻译结果
          for (const answerInfo of answerInfos) {
            if (answerInfo.text) {
              try {
                // 解析嵌套的 JSON 字符串
                const innerJson = JSON.parse(answerInfo.text);

                // 检查是否包含翻译内容
                if (innerJson.list && innerJson.list.length > 0) {
                  const content = innerJson.list[0].content;
                  if (content && content.text) {
                    // 累积翻译内容
                    accumulatedTranslation += content.text;
                    // console.log('累积翻译内容:', content.text);
                  }
                }
              } catch (innerParseError) {
                console.log('解析内层JSON失败:', innerParseError);
                continue;
              }
            }
          }
        }
      } catch (lineParseError) {
        console.log('解析行数据失败:', lineParseError);
        continue; // 继续处理下一行
      }
    }

    // 只有在流结束且有累积内容时才返回结果
    if (isStreamEnded && accumulatedTranslation.trim()) {
      // 在返回结果前再次检查翻译状态
      const currentState = getGlobalTranslationState()
      if (!currentState.isPageTranslated) {
        console.log('翻译已被取消，丢弃翻译结果:', requestId)
        removeActiveRequest(requestId)
        return
      }

      // 清理累积的翻译结果
      let finalTranslation = accumulatedTranslation
        .replace(/^翻译结果[：:]\s*/, '')
        .replace(/###Translation.*$/, '')
        .trim();

      console.log('最终翻译结果:', finalTranslation);

      // 移除请求记录
      removeActiveRequest(requestId)

      sendResponse({
        code: '0',
        result: finalTranslation,
      });
      return;
    }

    // 如果没有有效的翻译结果，返回错误
    console.log('未找到有效的翻译结果');
    removeActiveRequest(requestId)
    sendResponse({
      code: '1',
      error: '未找到有效的翻译结果',
    });
  } catch (error) {
    console.log('翻译错误:', error)
    removeActiveRequest(requestId)

    // 检查是否是取消错误
    if (error.name === 'AbortError') {
      console.log('翻译请求被取消:', requestId)
      return // 不调用sendResponse，避免错误回调
    }

    sendResponse({
      code: '1',
      error: error.message,
    })
  }
}

// 按字符数限制分组
function groupTextsByCharLimit(
  items: Array<{
    icon: HTMLElement
    textContent: string
    id: string
    paragraph: HTMLElement
    segmentContainer?: HTMLElement
    htmlStructure?: Array<{
      type: 'text' | 'element'
      content: string
      tagName?: string
      attributes?: Record<string, string>
    }>
  }>,
  maxChars: number
) {
  const groups: Array<
    Array<{
      icon: HTMLElement
      textContent: string
      id: string
      paragraph: HTMLElement
      segmentContainer?: HTMLElement
      htmlStructure?: Array<{
        type: 'text' | 'element'
        content: string
        tagName?: string
        attributes?: Record<string, string>
      }>
    }>
  > = []
  let currentGroup: Array<{
    icon: HTMLElement
    textContent: string
    id: string
    paragraph: HTMLElement
    segmentContainer?: HTMLElement
    htmlStructure?: Array<{
      type: 'text' | 'element'
      content: string
      tagName?: string
      attributes?: Record<string, string>
    }>
  }> = []
  let currentChars = 0

  items.forEach((item) => {
    const itemChars = item.textContent.length

    // 如果单个文本就超过限制，单独成组
    if (itemChars > maxChars) {
      if (currentGroup.length > 0) {
        groups.push(currentGroup)
        currentGroup = []
        currentChars = 0
      }
      groups.push([item])
      return
    }

    // 检查加入当前组是否会超过限制（包括分隔符的长度）
    const separatorChars = currentGroup.length > 0 ? 3 : 0 // ###的长度
    if (currentChars + separatorChars + itemChars <= maxChars) {
      currentGroup.push(item)
      currentChars += separatorChars + itemChars
    } else {
      // 当前组已满，开始新组
      if (currentGroup.length > 0) {
        groups.push(currentGroup)
      }
      currentGroup = [item]
      currentChars = itemChars
    }
  })

  // 添加最后一组
  if (currentGroup.length > 0) {
    groups.push(currentGroup)
  }

  return groups
}

// 清理翻译文本中的转义字符
function cleanTranslationText(text: string): string {
  return (
    text
      // 处理转义字符，按正确顺序处理
      .replace(/\\"/g, '"') // 转义双引号 \" → "
      .replace(/\\'/g, "'") // 转义单引号 \' → '
      .replace(/\\n/g, ' ') // 转义换行符 \n → 空格
      .replace(/\\t/g, ' ') // 转义制表符 \t → 空格
      .replace(/\\r/g, ' ') // 转义回车符 \r → 空格
      .replace(/\\\\/g, '\\') // 转义反斜杠 \\ → \
      .replace(/\n/g, ' ') // 实际换行符 → 空格
      .replace(/\t/g, ' ') // 实际制表符 → 空格
      .replace(/\r/g, ' ') // 实际回车符 → 空格
      .replace(/\s+/g, ' ') // 多个空白字符 → 单个空格
      .trim()
  )
}

// 重建HTML结构
function reconstructHtmlStructure(
  translatedText: string,
  htmlStructure: Array<{
    type: 'text' | 'element'
    content: string
    tagName?: string
    attributes?: Record<string, string>
  }>
): DocumentFragment {
  const fragment = document.createDocumentFragment()

  // 将翻译后的文本按空格分割
  const translatedWords = translatedText.split(/\s+/)
  let wordIndex = 0

  for (const item of htmlStructure) {
    if (item.type === 'text') {
      // 对于纯文本，计算应该占用多少个翻译后的词
      const originalWords = item.content.split(/\s+/)
      const wordsToTake = Math.min(
        originalWords.length,
        translatedWords.length - wordIndex
      )

      if (wordsToTake > 0) {
        const textNode = document.createTextNode(
          translatedWords.slice(wordIndex, wordIndex + wordsToTake).join(' ')
        )
        fragment.appendChild(textNode)
        wordIndex += wordsToTake
      }
    } else if (item.type === 'element' && item.tagName) {
      // 对于HTML元素，重建元素并填入翻译后的内容
      const element = document.createElement(item.tagName)

      // 恢复属性
      if (item.attributes) {
        Object.entries(item.attributes).forEach(([name, value]) => {
          element.setAttribute(name, value)
        })
      }

      // 计算这个元素应该占用多少个翻译后的词
      const originalWords = item.content.split(/\s+/)
      const wordsToTake = Math.min(
        originalWords.length,
        translatedWords.length - wordIndex
      )

      if (wordsToTake > 0) {
        element.textContent = translatedWords
          .slice(wordIndex, wordIndex + wordsToTake)
          .join(' ')
        wordIndex += wordsToTake
      }

      fragment.appendChild(element)
    }
  }

  // 如果还有剩余的翻译词汇，作为文本节点添加
  if (wordIndex < translatedWords.length) {
    const remainingText = translatedWords.slice(wordIndex).join(' ')
    if (remainingText.trim()) {
      fragment.appendChild(document.createTextNode(' ' + remainingText))
    }
  }

  return fragment
}

// 更新图标并创建翻译结果元素
function updateIconWithTranslation(
  icon: HTMLElement,
  translation: string,
  paragraph?: HTMLElement,
  htmlStructure?: Array<{
    type: 'text' | 'element'
    content: string
    tagName?: string
    attributes?: Record<string, string>
  }>,
  segmentContainer?: HTMLElement
) {
  // 在插入翻译结果前检查翻译状态
  const state = getGlobalTranslationState()
  if (!state.isPageTranslated) {
    console.log('翻译已被取消，跳过插入翻译结果')
    // 隐藏图标但不插入翻译结果
    icon.style.display = 'none'
    return
  }

  // 使用新的清理函数处理翻译结果
  const cleanTranslation = cleanTranslationText(translation)
  console.log('翻译结果:', {
    translation,
    cleanTranslation,
    paragraph
  })

  // 获取原文内容进行比较
  let originalText = ''
  if (paragraph) {
    // 获取段落中的文本内容，排除已有的翻译结果
    const textNodes = Array.from(paragraph.childNodes)
      .filter(
        (node: HTMLElement) =>
          node.nodeType === Node.TEXT_NODE ||
          (node.nodeType === Node.ELEMENT_NODE &&
            !node.classList?.contains(translateIcon) &&
            !node.classList?.contains(translationResult))
      )
      .map((node) => node.textContent?.trim() || '')
      .join(' ')
      .replace(/\s+/g, ' ')
      .trim()
    originalText = textNodes
  }

  // 如果翻译结果和原文一致，则不创建新的翻译元素
  if (cleanTranslation === originalText) {
    // 直接隐藏图标，不添加翻译结果
    icon.style.display = 'none'
    return
  }

  // 隐藏加载图标
  icon.style.display = 'none'

  // 创建翻译结果元素
  const translationElement = document.createElement('div')
  translationElement.className = translationResult
  translationElement.style.cssText = `
    display: block;
    margin: 5px 0;
  `

  // 如果有HTML结构信息，重建结构化的翻译结果
  if (htmlStructure && htmlStructure.length > 0) {
    const reconstructedFragment = reconstructHtmlStructure(
      cleanTranslation,
      htmlStructure
    )
    translationElement.appendChild(reconstructedFragment)
  } else {
    // 否则使用纯文本
    translationElement.innerText = cleanTranslation
  }
  icon.parentNode?.insertBefore(translationElement, icon.nextSibling)
  return
  // 将翻译结果插入到合适的位置
  if (segmentContainer && segmentContainer === icon) {
    // 特殊情况：segmentContainer就是图标本身，说明使用了新的无包装容器方案
    if (paragraph && (paragraph as any)._translationMap) {
      const translationMap = (paragraph as any)._translationMap as Map<HTMLElement, {
        segmentIndex: number
        segmentInfo: any
        insertionPoint: Node | null
      }>

      const mapInfo = translationMap.get(icon)
      if (mapInfo && mapInfo.insertionPoint) {
        // 在记录的插入点后面插入翻译结果
        if (mapInfo.insertionPoint.nextSibling) {
          paragraph.insertBefore(translationElement, mapInfo.insertionPoint.nextSibling)
        } else {
          paragraph.appendChild(translationElement)
        }
      } else {
        // 备用方案：插入到图标后面
        icon.parentNode?.insertBefore(translationElement, icon.nextSibling)
      }
    } else {
      // 备用方案：插入到图标后面
      icon.parentNode?.insertBefore(translationElement, icon.nextSibling)
    }
  } else if (segmentContainer && segmentContainer !== icon) {
    // 传统方案：有真正的分段容器
    if (segmentContainer.parentNode) {
      segmentContainer.parentNode.insertBefore(translationElement, segmentContainer.nextSibling)
    } else {
      // 备用方案：插入到图标后面
      icon.parentNode?.insertBefore(translationElement, icon.nextSibling)
    }
  } else if (paragraph) {
    // 否则插入到段落中
    paragraph.appendChild(translationElement)
  } else {
    // 如果没有段落信息，插入到图标后面
    icon.parentNode?.insertBefore(translationElement, icon.nextSibling)
  }
}

// 添加文本到翻译队列
function addToTranslationQueue(
  icon: HTMLElement,
  textContent: string,
  paragraph: HTMLElement,
  htmlStructure?: Array<{
    type: 'text' | 'element'
    content: string
    tagName?: string
    attributes?: Record<string, string>
  }>,
  segmentContainer?: HTMLElement
) {
  const id = generateUniqueId()
  textCollectionQueue.push({ icon, textContent, id, paragraph, htmlStructure, segmentContainer })

  // 重置定时器
  if (batchProcessTimer) {
    clearTimeout(batchProcessTimer)
  }

  // 设置批量处理定时器
  batchProcessTimer = setTimeout(() => {
    processBatchTranslation()
  }, BATCH_DELAY)
}

async function handleTranslateTextContent(
  icon: HTMLElement,
  textContent: string,
  paragraph: HTMLElement,
  htmlStructure?: Array<{
    type: 'text' | 'element'
    content: string
    tagName?: string
    attributes?: Record<string, string>
  }>,
  segmentContainer?: HTMLElement
) {
  // 添加到翻译队列
  addToTranslationQueue(icon, textContent, paragraph, htmlStructure, segmentContainer)
}

// 提取段落的结构化内容（保留HTML结构信息）
function extractStructuredContent(paragraph: HTMLElement): {
  textContent: string
  htmlStructure: Array<{
    type: 'text' | 'element'
    content: string
    tagName?: string
    attributes?: Record<string, string>
  }>
} {
  const structure: Array<{
    type: 'text' | 'element'
    content: string
    tagName?: string
    attributes?: Record<string, string>
  }> = []

  const textParts: string[] = []

  function processNode(node: Node) {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent?.trim()
      if (text) {
        structure.push({
          type: 'text',
          content: text,
        })
        textParts.push(text)
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as HTMLElement

      // 跳过翻译相关的元素
      if (
        element.classList.contains(translateIcon) ||
        element.classList.contains(translationResult)
      ) {
        return
      }

      // 检查是否在黑名单中
      if (blankNodeList.includes(element.tagName)) {
        return
      }

      // 对于内联元素（如 a, strong, code, em 等），保留其结构
      const inlineElements = [
        'A',
        'STRONG',
        'B',
        'EM',
        'I',
        'CODE',
        'SPAN',
        'MARK',
        'U',
        'S',
        'SUB',
        'SUP',
      ]

      if (inlineElements.includes(element.tagName)) {
        // 获取元素的属性
        const attributes: Record<string, string> = {}
        for (let i = 0; i < element.attributes.length; i++) {
          const attr = element.attributes[i]
          attributes[attr.name] = attr.value
        }

        // 递归处理子节点，获取内部文本
        const childText = Array.from(element.childNodes)
          .map((child) => child.textContent?.trim() || '')
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim()

        if (childText) {
          structure.push({
            type: 'element',
            content: childText,
            tagName: element.tagName.toLowerCase(),
            attributes,
          })
          textParts.push(childText)
        }
      } else {
        // 对于其他元素，递归处理子节点
        Array.from(element.childNodes).forEach(processNode)
      }
    }
  }

  // 处理段落的所有子节点
  Array.from(paragraph.childNodes).forEach(processNode)

  return {
    textContent: textParts.join(' ').replace(/\s+/g, ' ').trim(),
    htmlStructure: structure,
  }
}

// 添加翻译图标
function addTranslateIcon(paragraph: HTMLElement, _textNodes?: Text[]) {
  // 检查段落是否包含br标签，如果包含则拆分处理
  if (shouldSplitParagraph(paragraph)) {
    addTranslateIconsForSplitParagraph(paragraph)
  } else {
    addSingleTranslateIcon(paragraph)
  }
}

// 为拆分的段落添加多个翻译图标（直接在原结构中插入）
function addTranslateIconsForSplitParagraph(paragraph: HTMLElement) {
  // 注意：此函数调用前，段落已经被标记为splitParagraph，这里不需要重复检查

  const segments = analyzeParagraphSegments(paragraph)

  if (segments.length <= 1) {
    // 如果拆分后只有一个段落，移除拆分标记，使用原有逻辑
    paragraph.classList.remove(splitParagraph)
    addSingleTranslateIcon(paragraph)
    return
  }

  // console.log(`开始拆分段落，共${segments.length}个分段:`, paragraph.textContent?.substring(0, 50) + '...')

  // 重建段落内容，在每个分段后直接插入翻译UI
  rebuildParagraphWithTranslationUI(paragraph, segments)
}

// 重建段落内容，完全不使用包装容器，直接插入原始HTML
function rebuildParagraphWithTranslationUI(paragraph: HTMLElement, segments: Array<{
  htmlContent: string
  textContent: string
  htmlStructure: Array<{
    type: 'text' | 'element'
    content: string
    tagName?: string
    attributes?: Record<string, string>
  }>
}>) {
  // 清空原段落内容（保留样式）
  paragraph.innerHTML = ''

  // 存储翻译图标和对应的分段信息，用于后续插入翻译结果
  const translationMap = new Map<HTMLElement, {
    segmentIndex: number
    segmentInfo: typeof segments[0]
    insertionPoint: Node | null
  }>()

  segments.forEach((segment, index) => {
    // console.log(`处理分段 ${index + 1}/${segments.length}:`, segment.textContent.substring(0, 30) + '...')

    // 1. 直接插入分段的HTML内容（不使用任何包装容器）
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = segment.htmlContent

    // 将临时容器中的所有子节点直接移动到段落中
    while (tempDiv.firstChild) {
      paragraph.appendChild(tempDiv.firstChild)
    }

    // 记录当前插入位置，用于后续插入翻译结果
    const insertionPoint = paragraph.lastChild

    // 2. 创建并插入翻译图标
    const icon = document.createElement('span')
    icon.className = translateIcon
    icon.style.cssText = `
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-left: 4px;
      background-image: url(${loadingIcon});
      background-size: contain;
      background-repeat: no-repeat;
      vertical-align: middle;
    `
    paragraph.appendChild(icon)

    // 3. 存储翻译图标和相关信息，用于后续插入翻译结果
    translationMap.set(icon, {
      segmentIndex: index,
      segmentInfo: segment,
      insertionPoint: insertionPoint
    })

    // 4. 处理该分段的翻译（传递特殊的容器信息）
    handleTranslateTextContent(
      icon,
      segment.textContent,
      paragraph,
      segment.htmlStructure,
      icon // 传递图标本身，在updateIconWithTranslation中会特殊处理
    )

    // 5. 如果不是最后一个分段，添加换行
    if (index < segments.length - 1) {
      const br = document.createElement('br')
      paragraph.appendChild(br)
    }
  })

    // 将翻译映射信息存储到段落元素上，供updateIconWithTranslation使用
    ; (paragraph as any)._translationMap = translationMap
}

// 为单个段落添加翻译图标（原有逻辑）
function addSingleTranslateIcon(paragraph: HTMLElement) {
  const icon = document.createElement('span')
  icon.className = translateIcon
  icon.style.cssText = `
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-left: 4px;
    background-image: url(${loadingIcon});
    background-size: contain;
    background-repeat: no-repeat;
    vertical-align: middle;
  `

  // 提取结构化内容
  const structuredContent = extractStructuredContent(paragraph)

  // 将图标插入到段落末尾
  paragraph.appendChild(icon)

  // 处理翻译，传递结构化内容
  handleTranslateTextContent(
    icon,
    structuredContent.textContent,
    paragraph,
    structuredContent.htmlStructure
  )
}

function observerChildElement(element: HTMLElement) {
  if (!globalIntersectionObserver) {
    console.warn('IntersectionObserver 未初始化，跳过元素观察')
    return
  }

  const blockElements = element.querySelectorAll('*')
  blockElements.forEach((el) => {
    globalIntersectionObserver!.observe(el)
  })
}

const handleInsertDom = (data: any) => {
  console.log('handleInsertDom', {
    data,
    currentGroups
  });

  currentGroups.forEach((group: any) => {
    if (group.length > 1) {
      console.log('group.length > 1', {
        group
      });

      group.forEach((item: any, index: number) => {
        console.log('data[index]', {
          dataIndex: data[index],
          item
        });
        if (data[index]) {
          updateIconWithTranslation(
            item.icon,
            data[index],
            item.paragraph,
            item.htmlStructure,
            item.segmentContainer
          )
        }
      })
    } else {
      const item = group[0]
      console.log('group.length == 1', {
        item,
        dataResult: data.result
      });

      updateIconWithTranslation(
        item.icon,
        data.results[0],
        item.paragraph,
        item.htmlStructure,
        item.segmentContainer
      )
    }
  })
}



/**
 * 清理页面中所有翻译相关的元素
 */
function clearAllTranslationElements(): void {
  console.log('开始清理页面翻译元素...')

  // 清理所有翻译图标
  const translateIcons = document.querySelectorAll(`.${translateIcon}`)
  translateIcons.forEach(icon => {
    icon.remove()
  })

  // 清理所有翻译结果
  const translationResults = document.querySelectorAll(`.${translationResult}`)
  translationResults.forEach(result => {
    result.remove()
  })

  // 清理已拆分段落的标记
  const splitParagraphs = document.querySelectorAll(`.${splitParagraph}`)
  splitParagraphs.forEach(paragraph => {
    paragraph.classList.remove(splitParagraph)
    // 清理段落上的翻译映射信息
    if ((paragraph as any)._translationMap) {
      delete (paragraph as any)._translationMap
    }
  })

  // 清理分段包装容器
  const segmentWrappers = document.querySelectorAll(`.${segmentWrapper}`)
  segmentWrappers.forEach(wrapper => {
    // 将包装容器的内容移动到父元素中，然后删除包装容器
    const parent = wrapper.parentNode
    if (parent) {
      while (wrapper.firstChild) {
        parent.insertBefore(wrapper.firstChild, wrapper)
      }
      wrapper.remove()
    }
  })

  // 清空翻译队列
  textCollectionQueue = []

  // 清除批量处理定时器
  if (batchProcessTimer) {
    clearTimeout(batchProcessTimer)
    batchProcessTimer = null
  }

  // 重置当前分组
  currentGroups = []

  console.log('页面翻译元素清理完成')
}

/**
 * 取消页面翻译
 */
function cancelPageTranslation(): void {
  console.log('取消页面翻译...')

  // 1. 立即更新全局状态，阻止新的翻译请求和结果插入
  setPageTranslated(false)
  setTranslating(false)

  // 2. 停止所有观察者，防止新元素触发翻译
  stopGlobalObserver()

  // 3. 取消所有进行中的翻译请求
  cancelAllActiveRequests()

  // 4. 清理所有翻译元素
  clearAllTranslationElements()

  console.log('页面翻译已取消')
}

function handleStartTranslate() {
  console.log('开始页面翻译...')

  // 1. 设置全局翻译状态
  setPageTranslated(true)
  setTranslating(true)

  // 2. 启动全局观察者
  startGlobalObserver()

  // 3. 处理当前页面所有可见元素
  observerChildElement(document.body)

  console.log('页面翻译已启动')
}

// 监听来自sidepanel侧边栏的消息
chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
  if (message.type === MessageType.CHECK_CONTENT_SCRIPT) {
    // 响应content script检查请求
    sendResponse({ loaded: true })
    return true
  }

  //侧栏中的翻译此页面会触发该事件
  if (message.type === MessageType.START_TRANSLATE) {
    // 直接开始翻译流程，跳过会话创建步骤
    console.log('收到START_TRANSLATE消息，开始页面翻译')

    // 确保全局状态在观察者启动前更新
    setPageTranslated(true)
    setTranslating(true)

    handleStartTranslate()
  }

  // 处理取消翻译消息
  if (message.type === MessageType.CANCEL_TRANSLATE) {
    console.log('injectTranslate: 收到取消翻译消息，开始执行取消操作')
    cancelPageTranslation()
    console.log('injectTranslate: 取消翻译操作完成')
  }

  // if (message.type === MessageType.INSERT_DOM) {
  //   //在这里处理插入元素
  //   handleInsertDom(message.data)
  // }
})

export {
  handleStartTranslate,
  handleInsertDom,
  cancelPageTranslation,
  getGlobalTranslationState,
  setPageTranslated,
  setTranslating,
  startGlobalObserver,
  stopGlobalObserver
}
